package calendarMgr

import (
	"context"
	"fmt"
	"time"

	v1 "gtcms/api/v1"
	"gtcms/internal/service"

	"github.com/hablullah/go-hijri"
)

// sCalendarMgr 日历管理服务实现
type sCalendarMgr struct{}

func init() {
	service.RegisterCalendarMgr(New())
}

// New 创建日历管理服务实例
func New() service.ICalendarMgr {
	return &sCalendarMgr{}
}

// Init 初始化日历数据
func (s *sCalendarMgr) Init(ctx context.Context, req *v1.CalendarInitReq) (res *v1.CalendarInitRes, err error) {
	return
}

func calUmmAlQura(t *time.Time) (int64, int64, int64) {
	newYear := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	ummAlQuraDate, _ := hijri.CreateUmmAlQuraDate(newYear)
	return ummAlQuraDate.Year, ummAlQuraDate.Month, ummAlQuraDate.Day
}

func getPasaran(date time.Time) string {
	// 基准日期：1968-12-03 是 Slasa <PERSON>won (JDN = 2440213)
	baseDate := time.Date(1968, 12, 3, 0, 0, 0, 0, time.UTC)
	basePasaran := 0 // Kliwon

	// 计算目标日期的 JDN（简化为天数差）
	daysDiff := int(date.Sub(baseDate).Hours() / 24)
	pasaranIndex := (basePasaran + daysDiff) % 5
	if pasaranIndex < 0 {
		pasaranIndex += 5
	}

	pasaranNames := []string{"Kliwon", "Legi", "Pahing", "Pon", "Wage"}
	weekdayNames := []string{"Minggu", "Senin", "Selasa", "Rebo", "Kemis", "Jumat", "Setu"}

	// 获取星期
	weekday := int(date.Weekday())
	return fmt.Sprintf("%s %s", weekdayNames[weekday], pasaranNames[pasaranIndex])
}
