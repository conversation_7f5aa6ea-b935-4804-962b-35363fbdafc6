// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	v1 "gtcms/api/v1"
)

type (
	ICalendarMgr interface {
		// Init 初始化日历数据
		Init(ctx context.Context, req *v1.CalendarInitReq) (res *v1.CalendarInitRes, err error)
	}
)

var (
	localCalendarMgr ICalendarMgr
)

func CalendarMgr() ICalendarMgr {
	if localCalendarMgr == nil {
		panic("implement not found for interface ICalendarMgr, forgot register?")
	}
	return localCalendarMgr
}

func RegisterCalendarMgr(i ICalendarMgr) {
	localCalendarMgr = i
}
